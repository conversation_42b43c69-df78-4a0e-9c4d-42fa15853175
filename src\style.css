/* CSS Custom Properties */
:root {
  --color-primary: #2563eb;
  --color-text: #1f2937;
  --color-text-light: #6b7280;
  --color-background: #ffffff;
  --color-surface: #f9fafb;
  --color-border: #e5e7eb;
  
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-base: 16px;
  --line-height-base: 1.5;
  
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  
  --border-radius: 0.375rem;
  --transition: all 0.2s ease;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --color-text: #f9fafb;
    --color-text-light: #d1d5db;
    --color-background: #111827;
    --color-surface: #1f2937;
    --color-border: #374151;
  }
}

/* Reset and base styles */
* {
  box-sizing: border-box;
}

html {
  font-size: var(--font-size-base);
  line-height: var(--line-height-base);
}

body {
  margin: 0;
  font-family: var(--font-family);
  color: var(--color-text);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Layout */
#app {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-md);
}

.container {
  text-align: center;
  max-width: 600px;
  width: 100%;
}

/* Typography */
h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 var(--spacing-lg) 0;
  color: var(--color-text);
}

p {
  color: var(--color-text-light);
  margin: 0;
}

/* Utilities */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}